<template>
  <div class="profit-chart-container">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'Profit<PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      handler(newData) {
        console.log('ProfitChart received data:', newData);
        this.renderChart(newData);
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.initChart();
    this.renderChart(this.chartData);
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer);
      }
    },
    
    renderChart(data) {
      if (!this.chart) {
        return;
      }

      // 如果没有数据，显示空状态
      if (!data || data.length === 0) {
        const emptyOption = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        };
        this.chart.setOption(emptyOption, true);
        return;
      }

      // 准备图表配置
      const option = this.getChartOption(data);

      // 渲染图表
      this.chart.setOption(option, true);
    },
    
    getChartOption(data) {
      // 生成颜色数组
      const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
      ];
      
      // 准备系列数据
      const series = data.map((device, index) => {
        // 确保device有name和list属性
        if (!device.name || !device.list || !Array.isArray(device.list)) {
          console.warn('Invalid device data:', device);
          return null;
        }

        const seriesData = device.list
          .filter(item => item && typeof item === 'object') // 过滤无效数据
          .map(item => [
            parseFloat(item.brentRatio) || 0,
            parseFloat(item.profit) || 0
          ])
          .filter(point => !isNaN(point[0]) && !isNaN(point[1])); // 过滤NaN值

        return {
          name: device.name,
          type: 'line',
          data: seriesData,
          smooth: 0.3, // 使用数值控制平滑程度，0.3提供更好的平滑效果
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3, // 增加线条宽度使其更明显
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 4
            }
          }
        };
      }).filter(series => series !== null); // 移除无效的系列
      
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            if (!params || params.length === 0) return '';

            let result = `<div style="font-weight: bold; margin-bottom: 5px;">指数变化率: ${params[0].value[0]}%</div>`;
            // 去重处理，确保每个装置只显示一次
            const uniqueParams = params.filter((param, index, self) =>
              index === self.findIndex(p => p.seriesName === param.seriesName)
            );

            uniqueParams.forEach(param => {
              const color = param.color;
              // 数据已经是万元单位，直接显示
              const profitValue = param.value[1].toFixed(1);
              result += `<div style="margin: 2px 0;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: <strong>${profitValue}万元</strong>
              </div>`;
            });
            return result;
          }
        },
        legend: {
          data: data.map(device => device.name),
          bottom: 10,
          type: 'scroll',
          orient: 'horizontal'
        },
        grid: {
          left: '8%',
          right: '5%',
          bottom: '20%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '指数变化率(%)',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 12,
            color: '#666'
          },
          axisLabel: {
            formatter: '{value}%',
            color: '#666'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#d0d0d0'
            }
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: '#d0d0d0'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '利润(万元)',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            fontSize: 12,
            color: '#666'
          },
          axisLabel: {
            formatter: function(value) {
              // 数据已经是万元单位，直接显示
              return value.toFixed(1);
            },
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: '#e0e0e0',
              width: 1
            }
          }
        },
        series: series,
        animation: true,
        animationDuration: 1000
      };
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style scoped>
.profit-chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.chart-container {
  width: 100%;
  height: 400px;
}
</style>
